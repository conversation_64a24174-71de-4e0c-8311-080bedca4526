<?php
/**
 * Common Functions
 *
 * This file contains common utility functions used throughout the application.
 */

/**
 * Get the current language
 *
 * @return string The current language code (en or ar)
 */
function getCurrentLanguage() {
    return isset($_SESSION['language']) ? $_SESSION['language'] : 'en';
}

/**
 * Set the current language
 *
 * @param string $language The language code (en or ar)
 * @return void
 */
function setLanguage($language) {
    if (in_array($language, ['en', 'ar'])) {
        $_SESSION['language'] = $language;
    }
}

/**
 * Get a translated string
 *
 * @param string $key The translation key
 * @param array $params Optional parameters for string formatting
 * @return string The translated string
 */
function __($key, $params = []) {
    $language = getCurrentLanguage();
    $translations = include __DIR__ . "/../languages/{$language}.php";

    if (isset($translations[$key])) {
        $text = $translations[$key];

        // Replace parameters in the string
        if (!empty($params)) {
            foreach ($params as $i => $param) {
                $text = str_replace("{{$i}}", $param, $text);
            }
        }

        return $text;
    }

    return $key;
}

/**
 * Redirect to a URL
 *
 * @param string $url The URL to redirect to
 * @return void
 */
function redirect($url) {
    header("Location: {$url}");
    exit;
}

/**
 * Check if the current request is an AJAX request
 *
 * @return bool True if the request is an AJAX request, false otherwise
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Generate a random string
 *
 * @param int $length The length of the string
 * @return string The random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

/**
 * Format a date
 *
 * @param string $date The date to format
 * @param string $format The format to use
 * @return string The formatted date
 */
function formatDate($date, $format = 'Y-m-d') {
    return date($format, strtotime($date));
}

/**
 * Sanitize input
 *
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate a QR code for a device
 *
 * @param int $deviceId The device ID
 * @param string $serialNumber The device serial number
 * @return string The path to the QR code image
 */
function generateQRCode($deviceId, $serialNumber) {
    // Check if GD extension is available
    if (!extension_loaded('gd')) {
        error_log("GD extension is not available. QR code generation failed.");
        return false;
    }

    // Include the QR code library
    require_once __DIR__ . '/../vendor/phpqrcode/qrlib.php';

    // Create the QR code data
    $qrData = json_encode([
        'id' => $deviceId,
        'serial' => $serialNumber,
        'url' => 'ticket/create/' . $deviceId
    ]);

    // Generate a unique filename
    $filename = 'qrcode_' . $deviceId . '_' . time() . '.png';
    $filepath = __DIR__ . '/../uploads/qrcodes/' . $filename;

    // Generate the QR code
    QRcode::png($qrData, $filepath, 'L', 10, 2);

    return 'uploads/qrcodes/' . $filename;
}

/**
 * Check if a user has a specific permission
 *
 * @param string $permission The permission to check
 * @return bool True if the user has the permission, false otherwise
 */
function hasPermission($permission) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isset($_SESSION['user'])) {
        return false;
    }

    // Admin has all permissions
    if ($_SESSION['user']['role'] === 'admin') {
        return true;
    }

    // Check user permissions
    if (isset($_SESSION['user']['permissions'])) {
        $permissions = is_array($_SESSION['user']['permissions'])
            ? $_SESSION['user']['permissions']
            : json_decode($_SESSION['user']['permissions'], true);

        return is_array($permissions) && in_array($permission, $permissions);
    }

    return false;
}

/**
 * Get the current user
 *
 * @return array|null The current user or null if not logged in
 */
function getCurrentUser() {
    return isset($_SESSION['user']) ? $_SESSION['user'] : null;
}

/**
 * Check if a user is logged in
 *
 * @return bool True if the user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user']);
}

/**
 * Set a flash message
 *
 * @param string $type The message type (success, error, warning, info)
 * @param string $message The message
 * @return void
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Get and clear the flash message
 *
 * @return array|null The flash message or null if none exists
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }

    return null;
}

/**
 * Get the base URL of the application
 *
 * @return string The base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);

    return $protocol . '://' . $host . $path;
}

/**
 * Get the current page URL
 *
 * @return string The current page URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];

    return $protocol . '://' . $host . $uri;
}

/**
 * Check if the current page is the active page
 *
 * @param string $page The page to check
 * @return bool True if the current page is the active page, false otherwise
 */
function isActivePage($page) {
    $currentPage = basename($_SERVER['PHP_SELF']);
    return $currentPage === $page;
}

/**
 * Get the client IP address
 *
 * @return string The client IP address
 */
function getClientIp() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }

    return $ip;
}

/**
 * Get status color for badges
 *
 * @param string $status The status
 * @return string The color class
 */
function getStatusColor($status) {
    switch ($status) {
        case 'operational':
            return 'success';
        case 'under_maintenance':
            return 'warning';
        case 'out_of_order':
            return 'danger';
        case 'retired':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Get maintenance status color for badges
 *
 * @param string $status The maintenance status
 * @return string The color class
 */
function getMaintenanceStatusColor($status) {
    switch ($status) {
        case 'scheduled':
            return 'primary';
        case 'in_progress':
            return 'warning';
        case 'completed':
            return 'success';
        case 'cancelled':
            return 'secondary';
        case 'overdue':
            return 'danger';
        default:
            return 'secondary';
    }
}

/**
 * Get priority color for badges
 *
 * @param string $priority The priority level
 * @return string The color class
 */
function getPriorityColor($priority) {
    switch ($priority) {
        case 'low':
            return 'success';
        case 'medium':
            return 'warning';
        case 'high':
            return 'danger';
        case 'urgent':
            return 'dark';
        default:
            return 'secondary';
    }
}

/**
 * Get ticket status color for badges
 *
 * @param string $status The ticket status
 * @return string The color class
 */
function getTicketStatusColor($status) {
    switch ($status) {
        case 'open':
            return 'primary';
        case 'in_progress':
            return 'warning';
        case 'resolved':
            return 'success';
        case 'closed':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Get role color for badges
 *
 * @param string $role The user role
 * @return string The color class
 */
function getRoleColor($role) {
    switch ($role) {
        case 'admin':
            return 'danger';
        case 'manager':
            return 'warning';
        case 'technician':
            return 'info';
        case 'user':
            return 'secondary';
        default:
            return 'secondary';
    }
}

/**
 * Get notification icon
 *
 * @param string $type The notification type
 * @return string The icon class
 */
function getNotificationIcon($type) {
    switch ($type) {
        case 'maintenance':
            return 'wrench';
        case 'ticket':
            return 'exclamation-triangle';
        case 'system':
            return 'cog';
        case 'warning':
            return 'exclamation-circle';
        case 'info':
            return 'info-circle';
        default:
            return 'bell';
    }
}

/**
 * Get notification color
 *
 * @param string $type The notification type
 * @return string The color class
 */
function getNotificationColor($type) {
    switch ($type) {
        case 'maintenance':
            return 'warning';
        case 'ticket':
            return 'danger';
        case 'system':
            return 'info';
        case 'warning':
            return 'warning';
        case 'info':
            return 'info';
        default:
            return 'primary';
    }
}

/**
 * Log an action
 *
 * @param string $action The action to log
 * @param string $details The details of the action
 * @return void
 */
function logAction($action, $details = '') {
    global $pdo;

    $userId = isset($_SESSION['user']) ? $_SESSION['user']['id'] : 0;
    $ip = getClientIp();

    $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)");
    $stmt->execute([$userId, $action, $details, $ip]);
}

<?php
/**
 * Tickets List View
 * 
 * This file displays the list of support tickets.
 */

// Set page title
$pageTitle = __('tickets');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('tickets'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/tickets/create" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i><?php echo __('create_ticket'); ?>
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/tickets" class="row g-3">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="open" <?php echo ($status == 'open') ? 'selected' : ''; ?>><?php echo __('open'); ?></option>
                    <option value="in_progress" <?php echo ($status == 'in_progress') ? 'selected' : ''; ?>><?php echo __('in_progress'); ?></option>
                    <option value="resolved" <?php echo ($status == 'resolved') ? 'selected' : ''; ?>><?php echo __('resolved'); ?></option>
                    <option value="closed" <?php echo ($status == 'closed') ? 'selected' : ''; ?>><?php echo __('closed'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="priority" class="form-label"><?php echo __('priority'); ?></label>
                <select class="form-select" id="priority" name="priority">
                    <option value=""><?php echo __('all_priorities'); ?></option>
                    <option value="low" <?php echo ($priority == 'low') ? 'selected' : ''; ?>><?php echo __('low'); ?></option>
                    <option value="medium" <?php echo ($priority == 'medium') ? 'selected' : ''; ?>><?php echo __('medium'); ?></option>
                    <option value="high" <?php echo ($priority == 'high') ? 'selected' : ''; ?>><?php echo __('high'); ?></option>
                    <option value="urgent" <?php echo ($priority == 'urgent') ? 'selected' : ''; ?>><?php echo __('urgent'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="search" class="form-label"><?php echo __('search'); ?></label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>" placeholder="<?php echo __('search_tickets'); ?>">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tickets Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="tickets-table">
                <thead>
                    <tr>
                        <th><?php echo __('ticket_id'); ?></th>
                        <th><?php echo __('title'); ?></th>
                        <th><?php echo __('device'); ?></th>
                        <th><?php echo __('priority'); ?></th>
                        <th><?php echo __('status'); ?></th>
                        <th><?php echo __('created_by'); ?></th>
                        <th><?php echo __('created_at'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($tickets)): ?>
                        <tr>
                            <td colspan="8" class="text-center"><?php echo __('no_tickets'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($tickets as $ticket): ?>
                            <tr>
                                <td>
                                    <strong>#<?php echo $ticket['id']; ?></strong>
                                </td>
                                <td>
                                    <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>">
                                        <?php echo htmlspecialchars($ticket['title']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($ticket['device_id']): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $ticket['device_id']; ?>">
                                            <?php echo htmlspecialchars($ticket['device_name']); ?>
                                        </a>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($ticket['serial_number']); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_device'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getPriorityColor($ticket['priority']); ?>">
                                        <?php echo __($ticket['priority']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getTicketStatusColor($ticket['status']); ?>">
                                        <?php echo __($ticket['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($ticket['created_by_name']); ?></td>
                                <td>
                                    <?php 
                                    $createdAt = new DateTime($ticket['created_at']);
                                    echo $createdAt->format('Y-m-d H:i');
                                    ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if (hasPermission('manage_tickets') || $ticket['created_by'] == $_SESSION['user']['id']): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/tickets/edit/<?php echo $ticket['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php endif; ?>
                                        
                                        <?php if (hasPermission('manage_tickets')): ?>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDelete(<?php echo $ticket['id']; ?>, '<?php echo htmlspecialchars($ticket['title']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('delete_ticket_confirm'); ?></p>
                <p><strong id="ticketTitle"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(ticketId, ticketTitle) {
    document.getElementById('ticketTitle').textContent = ticketTitle;
    document.getElementById('deleteForm').action = '<?php echo getBaseUrl(); ?>/tickets/delete/' + ticketId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Auto-submit form when filters change
document.getElementById('hospital_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('priority').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>

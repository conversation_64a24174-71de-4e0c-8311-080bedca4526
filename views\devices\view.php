<?php
/**
 * View Device
 * 
 * This file displays the details of a device.
 */

// Set page title
$pageTitle = __('device_details');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('device_details'); ?>: <?php echo htmlspecialchars($device['name']); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/devices" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        
        <?php if (hasPermission('manage_devices')): ?>
        <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
        </a>
        
        <a href="<?php echo getBaseUrl(); ?>/devices/qrcode/<?php echo $device['id']; ?>" class="btn btn-info">
            <i class="fas fa-qrcode me-2"></i><?php echo __('qr_code'); ?>
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <!-- Device Information -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('device_information'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><?php echo __('name'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($device['name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('model'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($device['model']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('serial_number'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($device['serial_number']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('manufacturer'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($device['manufacturer']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('category'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($device['category'] ?? 'N/A'); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('status'); ?>:</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo getStatusColor($device['status']); ?>">
                                        <?php echo __($device['status']); ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><?php echo __('hospital'); ?>:</strong></td>
                                <td>
                                    <?php if (hasPermission('view_hospitals')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $device['hospital_id']; ?>">
                                            <?php echo htmlspecialchars($device['hospital_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($device['hospital_name']); ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('department'); ?>:</strong></td>
                                <td>
                                    <?php if (hasPermission('view_departments')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/departments/view/<?php echo $device['department_id']; ?>">
                                            <?php echo htmlspecialchars($device['department_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($device['department_name']); ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('location'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($device['location'] ?? 'N/A'); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('purchase_date'); ?>:</strong></td>
                                <td><?php echo date('Y-m-d', strtotime($device['purchase_date'])); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('warranty_expiry'); ?>:</strong></td>
                                <td>
                                    <?php 
                                    $warrantyExpiry = new DateTime($device['warranty_expiry']);
                                    $now = new DateTime();
                                    $isExpired = $warrantyExpiry < $now;
                                    ?>
                                    <span class="<?php echo $isExpired ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo $warrantyExpiry->format('Y-m-d'); ?>
                                        <?php if ($isExpired): ?>
                                            <i class="fas fa-exclamation-triangle ms-1" title="<?php echo __('warranty_expired'); ?>"></i>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('maintenance_interval'); ?>:</strong></td>
                                <td><?php echo $device['maintenance_interval'] ? $device['maintenance_interval'] . ' ' . __('days') : 'N/A'; ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <?php if (!empty($device['notes'])): ?>
                <div class="mt-3">
                    <strong><?php echo __('notes'); ?>:</strong>
                    <p class="mt-2"><?php echo nl2br(htmlspecialchars($device['notes'])); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Maintenance History -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('maintenance_history'); ?></h5>
                
                <?php if (hasPermission('manage_maintenance')): ?>
                <a href="<?php echo getBaseUrl(); ?>/maintenance/create?device_id=<?php echo $device['id']; ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-2"></i><?php echo __('schedule_maintenance'); ?>
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($maintenanceHistory)): ?>
                    <p class="text-muted"><?php echo __('no_maintenance_history'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('date'); ?></th>
                                    <th><?php echo __('type'); ?></th>
                                    <th><?php echo __('description'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                    <th><?php echo __('technician'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($maintenanceHistory as $maintenance): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d', strtotime($maintenance['scheduled_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($maintenance['title']); ?></td>
                                        <td><?php echo htmlspecialchars($maintenance['description']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo getMaintenanceStatusColor($maintenance['status']); ?>">
                                                <?php echo __($maintenance['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($maintenance['technician_name'] ?? 'N/A'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- QR Code -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('qr_code'); ?></h5>
            </div>
            <div class="card-body text-center">
                <?php if (!empty($device['qr_code']) && file_exists($device['qr_code'])): ?>
                    <img src="<?php echo getBaseUrl(); ?>/<?php echo $device['qr_code']; ?>" alt="QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                    <br>
                    <a href="<?php echo getBaseUrl(); ?>/<?php echo $device['qr_code']; ?>" download class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-2"></i><?php echo __('download'); ?>
                    </a>
                <?php else: ?>
                    <p class="text-muted"><?php echo __('no_qr_code'); ?></p>
                    <?php if (hasPermission('manage_devices')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/devices/generate_qr/<?php echo $device['id']; ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-qrcode me-2"></i><?php echo __('generate_qr'); ?>
                    </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <?php if (hasPermission('manage_devices') || hasPermission('manage_maintenance')): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('quick_actions'); ?></h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('manage_maintenance')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/maintenance/create?device_id=<?php echo $device['id']; ?>" class="btn btn-outline-primary">
                        <i class="fas fa-wrench me-2"></i><?php echo __('schedule_maintenance'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/tickets/create?device_id=<?php echo $device['id']; ?>" class="btn btn-outline-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo __('report_issue'); ?>
                    </a>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('manage_devices')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-edit me-2"></i><?php echo __('edit_device'); ?>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Device Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('statistics'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo count($maintenanceHistory); ?></h4>
                            <small class="text-muted"><?php echo __('maintenance_records'); ?></small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info"><?php echo $device['age_years'] ?? 'N/A'; ?></h4>
                        <small class="text-muted"><?php echo __('years_old'); ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>

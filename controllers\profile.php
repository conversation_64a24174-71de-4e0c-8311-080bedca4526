<?php
/**
 * Profile Controller
 * 
 * This file handles user profile-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userId = $currentUser['id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';

switch ($action) {
    case 'index':
        // Get the user
        $user = $userModel->getById($userId);
        
        // Include the profile view
        include 'views/profile/index.php';
        break;
        
    case 'edit':
        // Get the user
        $user = $userModel->getById($userId);
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userData = [
                'email' => $_POST['email'] ?? $user['email'],
                'full_name' => $_POST['full_name'] ?? $user['full_name'],
                'language' => $_POST['language'] ?? $user['language']
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($userData['email'])) {
                $errors['email'] = __('required_field');
            } elseif (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            } elseif ($userData['email'] !== $user['email'] && $userModel->getByEmail($userData['email'])) {
                $errors['email'] = __('email_taken');
            }
            
            if (empty($userData['full_name'])) {
                $errors['full_name'] = __('required_field');
            }
            
            // If no errors, update the user
            if (empty($errors)) {
                $result = updateUserProfile($userId, $userData);
                
                if ($result) {
                    // Log the action
                    logAction('update_profile', 'Updated profile');
                    
                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('profile')]));
                    
                    // Redirect to profile
                    redirect('profile');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }
        } else {
            $userData = $user;
            $errors = [];
        }
        
        // Include the profile edit view
        include 'views/profile/edit.php';
        break;
        
    case 'change_password':
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            // Validate data
            $errors = [];
            
            if (empty($currentPassword)) {
                $errors['current_password'] = __('required_field');
            }
            
            if (empty($newPassword)) {
                $errors['new_password'] = __('required_field');
            } elseif (strlen($newPassword) < 6) {
                $errors['new_password'] = __('password_too_short');
            }
            
            if (empty($confirmPassword)) {
                $errors['confirm_password'] = __('required_field');
            } elseif ($newPassword !== $confirmPassword) {
                $errors['confirm_password'] = __('passwords_not_match');
            }
            
            // If no errors, change the password
            if (empty($errors)) {
                $result = changePassword($userId, $currentPassword, $newPassword);
                
                if ($result) {
                    // Log the action
                    logAction('change_password', 'Changed password');
                    
                    // Set flash message
                    setFlashMessage('success', __('password_changed'));
                    
                    // Redirect to profile
                    redirect('profile');
                } else {
                    $errors['current_password'] = __('current_password_incorrect');
                }
            }
        } else {
            $errors = [];
        }
        
        // Include the profile change password view
        include 'views/profile/change_password.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}

<?php
/**
 * Test Script
 * 
 * This file tests the basic functionality of the application.
 */

// Start session
session_start();

// Set default timezone
date_default_timezone_set('UTC');

// Load configuration
require_once 'config/database.php';

// Load includes
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/notifications.php';

// Load models
require_once 'models/User.php';
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';
require_once 'models/Maintenance.php';
require_once 'models/Ticket.php';

// Initialize models
$userModel = new User($pdo);
$hospitalModel = new Hospital($pdo);
$departmentModel = new Department($pdo);
$deviceModel = new Device($pdo);
$maintenanceModel = new Maintenance($pdo);
$ticketModel = new Ticket($pdo);

// Check if the database is initialized
$stmt = $pdo->query("SHOW TABLES");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

if (empty($tables)) {
    echo "<h1>Database is not initialized</h1>";
    echo "<p>Please run the installation script first.</p>";
    exit;
}

// Test database connection
echo "<h1>Database Connection Test</h1>";
echo "<p>Connected to database: {$dbname}</p>";

// Test models
echo "<h2>Model Tests</h2>";

// Test User model
echo "<h3>User Model</h3>";
$users = $userModel->getAll();
echo "<p>Found " . count($users) . " users</p>";

// Test Hospital model
echo "<h3>Hospital Model</h3>";
$hospitals = $hospitalModel->getAll();
echo "<p>Found " . count($hospitals) . " hospitals</p>";

// Test Department model
echo "<h3>Department Model</h3>";
$departments = $departmentModel->getAll();
echo "<p>Found " . count($departments) . " departments</p>";

// Test Device model
echo "<h3>Device Model</h3>";
$devices = $deviceModel->getAll();
echo "<p>Found " . count($devices) . " devices</p>";

// Test Maintenance model
echo "<h3>Maintenance Model</h3>";
$schedules = $maintenanceModel->getAllSchedules();
echo "<p>Found " . count($schedules) . " maintenance schedules</p>";

// Test Ticket model
echo "<h3>Ticket Model</h3>";
$tickets = $ticketModel->getAll();
echo "<p>Found " . count($tickets) . " tickets</p>";

// Test QR code generation
echo "<h2>QR Code Generation Test</h2>";
if (extension_loaded('gd')) {
    echo "<p>GD extension is loaded</p>";
    
    // Include the QR code library
    require_once 'vendor/phpqrcode/qrlib.php';
    
    // Test QR code generation
    $testQrPath = 'uploads/qrcodes/test.png';
    if (QRcode::png('https://example.com', $testQrPath)) {
        echo "<p>QR code generated successfully: <img src='{$testQrPath}' alt='Test QR Code' style='width: 100px;'></p>";
    } else {
        echo "<p>Failed to generate QR code</p>";
    }
} else {
    echo "<p>GD extension is not loaded. QR code generation will not work.</p>";
}

// Test language support
echo "<h2>Language Support Test</h2>";
echo "<p>English: " . __('app_name') . "</p>";
setLanguage('ar');
echo "<p>Arabic: " . __('app_name') . "</p>";
setLanguage('en');

// Test authentication
echo "<h2>Authentication Test</h2>";
if (isLoggedIn()) {
    $user = getCurrentUser();
    echo "<p>Currently logged in as: {$user['username']}</p>";
    echo "<p>Role: {$user['role']}</p>";
    echo "<p>Permissions: " . implode(', ', $user['permissions']) . "</p>";
} else {
    echo "<p>Not logged in</p>";
    
    // Try to authenticate with default admin credentials
    if (authenticate('admin', 'admin123')) {
        $user = getCurrentUser();
        echo "<p>Successfully logged in as: {$user['username']}</p>";
        echo "<p>Role: {$user['role']}</p>";
        echo "<p>Permissions: " . implode(', ', $user['permissions']) . "</p>";
        
        // Test logout
        logout();
        echo "<p>Logged out successfully</p>";
    } else {
        echo "<p>Failed to authenticate with default credentials</p>";
    }
}

// Test notifications
echo "<h2>Notifications Test</h2>";
if (isLoggedIn()) {
    $user = getCurrentUser();
    $unreadCount = countUnreadNotifications($user['id']);
    echo "<p>Unread notifications: {$unreadCount}</p>";
    
    $notifications = getAllNotifications($user['id'], 5);
    echo "<p>Recent notifications:</p>";
    echo "<ul>";
    foreach ($notifications as $notification) {
        echo "<li>{$notification['title']}: {$notification['message']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>Not logged in, cannot test notifications</p>";
}

// Test complete
echo "<h2>Test Complete</h2>";
echo "<p>All tests completed successfully.</p>";
echo "<p><a href='index.php'>Go to Application</a></p>";

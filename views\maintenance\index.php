<?php
/**
 * Maintenance Schedules List View
 * 
 * This file displays the list of maintenance schedules.
 */

// Set page title
$pageTitle = __('maintenance_schedules');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('maintenance_schedules'); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/maintenance/logs" class="btn btn-outline-secondary">
            <i class="fas fa-list me-2"></i><?php echo __('maintenance_logs'); ?>
        </a>
        
        <?php if (hasPermission('manage_maintenance')): ?>
        <a href="<?php echo getBaseUrl(); ?>/maintenance/create_schedule" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i><?php echo __('schedule_maintenance'); ?>
        </a>
        <?php endif; ?>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/maintenance" class="row g-3">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="device_id" class="form-label"><?php echo __('device'); ?></label>
                <select class="form-select" id="device_id" name="device_id">
                    <option value=""><?php echo __('all_devices'); ?></option>
                    <?php foreach ($devices as $device): ?>
                        <option value="<?php echo $device['id']; ?>" <?php echo ($deviceId == $device['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($device['name'] . ' (' . $device['serial_number'] . ')'); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="scheduled" <?php echo ($status == 'scheduled') ? 'selected' : ''; ?>><?php echo __('scheduled'); ?></option>
                    <option value="completed" <?php echo ($status == 'completed') ? 'selected' : ''; ?>><?php echo __('completed'); ?></option>
                    <option value="overdue" <?php echo ($status == 'overdue') ? 'selected' : ''; ?>><?php echo __('overdue'); ?></option>
                    <option value="cancelled" <?php echo ($status == 'cancelled') ? 'selected' : ''; ?>><?php echo __('cancelled'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-secondary">
                        <i class="fas fa-search me-2"></i><?php echo __('filter'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Maintenance Schedules Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="schedules-table">
                <thead>
                    <tr>
                        <th><?php echo __('device'); ?></th>
                        <th><?php echo __('title'); ?></th>
                        <th><?php echo __('scheduled_date'); ?></th>
                        <th><?php echo __('frequency'); ?></th>
                        <th><?php echo __('status'); ?></th>
                        <th><?php echo __('priority'); ?></th>
                        <th><?php echo __('created_by'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($schedules)): ?>
                        <tr>
                            <td colspan="8" class="text-center"><?php echo __('no_maintenance_schedules'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($schedules as $schedule): ?>
                            <tr>
                                <td>
                                    <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $schedule['device_id']; ?>">
                                        <?php echo htmlspecialchars($schedule['device_name']); ?>
                                    </a>
                                    <br>
                                    <small class="text-muted"><?php echo htmlspecialchars($schedule['serial_number']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($schedule['title']); ?></td>
                                <td>
                                    <?php 
                                    $scheduledDate = new DateTime($schedule['scheduled_date']);
                                    $now = new DateTime();
                                    $isOverdue = $scheduledDate < $now && $schedule['status'] === 'scheduled';
                                    ?>
                                    <span class="<?php echo $isOverdue ? 'text-danger' : ''; ?>">
                                        <?php echo $scheduledDate->format('Y-m-d'); ?>
                                        <?php if ($isOverdue): ?>
                                            <i class="fas fa-exclamation-triangle ms-1"></i>
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        <?php echo __($schedule['frequency']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getMaintenanceStatusColor($schedule['status']); ?>">
                                        <?php echo __($schedule['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getPriorityColor($schedule['priority']); ?>">
                                        <?php echo __($schedule['priority']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($schedule['created_by_name'] ?? 'N/A'); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if (hasPermission('manage_maintenance')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_schedule/<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <?php if ($schedule['status'] === 'scheduled'): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-wrench"></i>
                                        </a>
                                        <?php endif; ?>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDelete(<?php echo $schedule['id']; ?>, '<?php echo htmlspecialchars($schedule['title']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('delete_schedule_confirm'); ?></p>
                <p><strong id="scheduleName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(scheduleId, scheduleName) {
    document.getElementById('scheduleName').textContent = scheduleName;
    document.getElementById('deleteForm').action = '<?php echo getBaseUrl(); ?>/maintenance/delete_schedule/' + scheduleId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Auto-submit form when filters change
document.getElementById('hospital_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('device_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

// Load devices when hospital changes
document.getElementById('hospital_id').addEventListener('change', function() {
    const hospitalId = this.value;
    const deviceSelect = document.getElementById('device_id');
    
    // Clear current options
    deviceSelect.innerHTML = '<option value=""><?php echo __('all_devices'); ?></option>';
    
    if (hospitalId) {
        // Fetch devices for the selected hospital
        fetch(`<?php echo getBaseUrl(); ?>/api/get_devices?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    data.data.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.id;
                        option.textContent = device.name + ' (' + device.serial_number + ')';
                        deviceSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading devices:', error);
            });
    }
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>

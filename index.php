<?php
/**
 * Medical Device Management System
 * 
 * Main entry point for the application.
 */

// Start session
session_start();

// Set default timezone
date_default_timezone_set('UTC');

// Load configuration
require_once 'config/database.php';

// Load includes
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/notifications.php';

// Load models
require_once 'models/User.php';
require_once 'models/Hospital.php';
require_once 'models/Department.php';
require_once 'models/Device.php';
require_once 'models/Maintenance.php';
require_once 'models/Ticket.php';

// Initialize models
$userModel = new User($pdo);
$hospitalModel = new Hospital($pdo);
$departmentModel = new Department($pdo);
$deviceModel = new Device($pdo);
$maintenanceModel = new Maintenance($pdo);
$ticketModel = new Ticket($pdo);

// Check if the database needs to be initialized
$stmt = $pdo->query("SHOW TABLES");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

if (empty($tables)) {
    // Database is empty, initialize it
    require_once 'install/install.php';
    exit;
}

// Update overdue maintenance schedules
$maintenanceModel->updateOverdueSchedules();

// Handle routing
$url = isset($_GET['url']) ? $_GET['url'] : '';
$url = rtrim($url, '/');
$url = filter_var($url, FILTER_SANITIZE_URL);
$url = explode('/', $url);

$controller = !empty($url[0]) ? $url[0] : 'dashboard';
$action = isset($url[1]) ? $url[1] : 'index';
$param = isset($url[2]) ? $url[2] : null;

// Check if user is logged in
if (!isLoggedIn() && $controller != 'login' && $controller != 'reset_password' && $controller != 'api') {
    redirect('login.php');
}

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], ['en', 'ar'])) {
    setLanguage($_GET['lang']);
    
    if (isLoggedIn()) {
        // Update user's language preference
        $userModel->update($_SESSION['user']['id'], [
            'language' => $_GET['lang'],
            'email' => $_SESSION['user']['email'],
            'full_name' => $_SESSION['user']['full_name'],
            'role' => $_SESSION['user']['role'],
            'hospital_id' => $_SESSION['user']['hospital_id']
        ]);
        
        $_SESSION['user']['language'] = $_GET['lang'];
    }
    
    // Redirect to the same page without the lang parameter
    $redirectUrl = strtok($_SERVER['REQUEST_URI'], '?');
    $queryParams = $_GET;
    unset($queryParams['lang']);
    
    if (!empty($queryParams)) {
        $redirectUrl .= '?' . http_build_query($queryParams);
    }
    
    redirect($redirectUrl);
}

// Route to the appropriate controller
switch ($controller) {
    case 'dashboard':
        require_once 'controllers/dashboard.php';
        break;
        
    case 'users':
        requirePermission('manage_users');
        require_once 'controllers/users.php';
        break;
        
    case 'hospitals':
        if ($action != 'view' && !hasPermission('manage_hospitals')) {
            requirePermission('manage_hospitals');
        }
        require_once 'controllers/hospitals.php';
        break;
        
    case 'departments':
        if ($action != 'view' && !hasPermission('manage_departments')) {
            requirePermission('manage_departments');
        }
        require_once 'controllers/departments.php';
        break;
        
    case 'devices':
        if (in_array($action, ['create', 'edit', 'delete']) && !hasPermission('manage_devices')) {
            requirePermission('manage_devices');
        }
        require_once 'controllers/devices.php';
        break;
        
    case 'maintenance':
        if (in_array($action, ['create', 'edit', 'delete']) && !hasPermission('manage_maintenance')) {
            requirePermission('manage_maintenance');
        }
        require_once 'controllers/maintenance.php';
        break;
        
    case 'tickets':
        if (in_array($action, ['edit', 'delete']) && !hasPermission('manage_tickets')) {
            requirePermission('manage_tickets');
        }
        require_once 'controllers/tickets.php';
        break;
        
    case 'reports':
        requirePermission('view_reports');
        require_once 'controllers/reports.php';
        break;
        
    case 'profile':
        require_once 'controllers/profile.php';
        break;
        
    case 'notifications':
        require_once 'controllers/notifications.php';
        break;
        
    case 'api':
        require_once 'controllers/api.php';
        break;
        
    case 'login':
        require_once 'login.php';
        break;
        
    case 'logout':
        logout();
        redirect('login.php');
        break;
        
    case 'reset_password':
        require_once 'reset_password.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        require_once 'views/404.php';
        break;
}

<?php
/**
 * Create Ticket View
 * 
 * This file displays the form to create a new support ticket.
 */

// Set page title
$pageTitle = __('create_ticket');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('create_ticket'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form action="<?php echo getBaseUrl(); ?>/tickets/store" method="post" id="ticketForm" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <?php if (!empty($errors['general'])): ?>
                <div class="alert alert-danger">
                    <?php echo $errors['general']; ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo !empty($errors['hospital_id']) ? 'is-invalid' : ''; ?>" id="hospital_id" name="hospital_id" required>
                            <option value=""><?php echo __('select_hospital'); ?></option>
                            <?php foreach ($hospitals as $hospital): ?>
                                <option value="<?php echo $hospital['id']; ?>" <?php echo ($ticketData['hospital_id'] ?? '') == $hospital['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($hospital['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (!empty($errors['hospital_id'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['hospital_id']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="device_id" class="form-label"><?php echo __('device'); ?></label>
                        <select class="form-select <?php echo !empty($errors['device_id']) ? 'is-invalid' : ''; ?>" id="device_id" name="device_id">
                            <option value=""><?php echo __('select_device_optional'); ?></option>
                            <?php foreach ($devices as $device): ?>
                                <option value="<?php echo $device['id']; ?>" <?php echo ($ticketData['device_id'] ?? '') == $device['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($device['name'] . ' (' . $device['serial_number'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (!empty($errors['device_id'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['device_id']; ?></div>
                        <?php endif; ?>
                        <div class="form-text"><?php echo __('device_optional_help'); ?></div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="title" class="form-label"><?php echo __('title'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo !empty($errors['title']) ? 'is-invalid' : ''; ?>" id="title" name="title" value="<?php echo htmlspecialchars($ticketData['title'] ?? ''); ?>" required>
                        <?php if (!empty($errors['title'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['title']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="priority" class="form-label"><?php echo __('priority'); ?></label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="low" <?php echo ($ticketData['priority'] ?? '') == 'low' ? 'selected' : ''; ?>><?php echo __('low'); ?></option>
                            <option value="medium" <?php echo ($ticketData['priority'] ?? 'medium') == 'medium' ? 'selected' : ''; ?>><?php echo __('medium'); ?></option>
                            <option value="high" <?php echo ($ticketData['priority'] ?? '') == 'high' ? 'selected' : ''; ?>><?php echo __('high'); ?></option>
                            <option value="urgent" <?php echo ($ticketData['priority'] ?? '') == 'urgent' ? 'selected' : ''; ?>><?php echo __('urgent'); ?></option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="category" class="form-label"><?php echo __('category'); ?></label>
                <select class="form-select" id="category" name="category">
                    <option value="technical" <?php echo ($ticketData['category'] ?? 'technical') == 'technical' ? 'selected' : ''; ?>><?php echo __('technical'); ?></option>
                    <option value="maintenance" <?php echo ($ticketData['category'] ?? '') == 'maintenance' ? 'selected' : ''; ?>><?php echo __('maintenance'); ?></option>
                    <option value="repair" <?php echo ($ticketData['category'] ?? '') == 'repair' ? 'selected' : ''; ?>><?php echo __('repair'); ?></option>
                    <option value="calibration" <?php echo ($ticketData['category'] ?? '') == 'calibration' ? 'selected' : ''; ?>><?php echo __('calibration'); ?></option>
                    <option value="training" <?php echo ($ticketData['category'] ?? '') == 'training' ? 'selected' : ''; ?>><?php echo __('training'); ?></option>
                    <option value="other" <?php echo ($ticketData['category'] ?? '') == 'other' ? 'selected' : ''; ?>><?php echo __('other'); ?></option>
                </select>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label"><?php echo __('description'); ?> <span class="text-danger">*</span></label>
                <textarea class="form-control <?php echo !empty($errors['description']) ? 'is-invalid' : ''; ?>" id="description" name="description" rows="6" required><?php echo htmlspecialchars($ticketData['description'] ?? ''); ?></textarea>
                <?php if (!empty($errors['description'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                <?php endif; ?>
                <div class="form-text"><?php echo __('description_help'); ?></div>
            </div>
            
            <div class="mb-3">
                <label for="attachments" class="form-label"><?php echo __('attachments'); ?></label>
                <input type="file" class="form-control" id="attachments" name="attachments[]" multiple accept="image/*,.pdf,.doc,.docx,.txt">
                <div class="form-text"><?php echo __('attachments_help'); ?></div>
            </div>
            
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="urgent_notification" name="urgent_notification" value="yes" <?php echo (!empty($ticketData['urgent_notification'])) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="urgent_notification">
                        <?php echo __('urgent_notification'); ?>
                    </label>
                    <div class="form-text"><?php echo __('urgent_notification_help'); ?></div>
                </div>
            </div>
            
            <div class="d-flex justify-content-end">
                <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-secondary me-2"><?php echo __('cancel'); ?></a>
                <button type="submit" class="btn btn-primary"><?php echo __('create_ticket'); ?></button>
            </div>
        </form>
    </div>
</div>

<script>
// Load devices when hospital changes
document.getElementById('hospital_id').addEventListener('change', function() {
    const hospitalId = this.value;
    const deviceSelect = document.getElementById('device_id');
    
    // Clear current options
    deviceSelect.innerHTML = '<option value=""><?php echo __('select_device_optional'); ?></option>';
    
    if (hospitalId) {
        // Fetch devices for the selected hospital
        fetch(`<?php echo getBaseUrl(); ?>/api/get_devices?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    data.data.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.id;
                        option.textContent = device.name + ' (' + device.serial_number + ')';
                        deviceSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading devices:', error);
            });
    }
});

// Auto-fill title based on device selection
document.getElementById('device_id').addEventListener('change', function() {
    const deviceSelect = this;
    const titleInput = document.getElementById('title');
    
    if (deviceSelect.value && !titleInput.value) {
        const deviceText = deviceSelect.options[deviceSelect.selectedIndex].text;
        const deviceName = deviceText.split(' (')[0];
        titleInput.value = 'Issue with ' + deviceName;
    }
});

// File upload validation
document.getElementById('attachments').addEventListener('change', function() {
    const files = this.files;
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.size > maxSize) {
            alert('<?php echo __('file_too_large'); ?>: ' + file.name);
            this.value = '';
            return;
        }
        
        if (!allowedTypes.includes(file.type)) {
            alert('<?php echo __('file_type_not_allowed'); ?>: ' + file.name);
            this.value = '';
            return;
        }
    }
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>

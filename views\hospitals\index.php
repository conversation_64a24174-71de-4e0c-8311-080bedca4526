<?php
/**
 * Hospitals List View
 * 
 * This file displays the list of hospitals.
 */

// Set page title
$pageTitle = __('hospitals');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('hospitals_list'); ?></h1>
    
    <?php if (hasPermission('manage_hospitals')): ?>
    <a href="<?php echo getBaseUrl(); ?>/hospitals/create" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i><?php echo __('add_hospital'); ?>
    </a>
    <?php endif; ?>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="hospitals-table">
                <thead>
                    <tr>
                        <th><?php echo __('name'); ?></th>
                        <th><?php echo __('city'); ?></th>
                        <th><?php echo __('country'); ?></th>
                        <th><?php echo __('phone'); ?></th>
                        <th><?php echo __('departments'); ?></th>
                        <th><?php echo __('devices'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($hospitals)): ?>
                        <tr>
                            <td colspan="7" class="text-center"><?php echo __('no_hospitals'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($hospitals as $hospital): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($hospital['name']); ?></td>
                                <td><?php echo htmlspecialchars($hospital['city']); ?></td>
                                <td><?php echo htmlspecialchars($hospital['country']); ?></td>
                                <td><?php echo htmlspecialchars($hospital['phone']); ?></td>
                                <td><?php echo (int)$hospital['department_count']; ?></td>
                                <td><?php echo (int)$hospital['device_count']; ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $hospital['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if (hasPermission('manage_hospitals')): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/hospitals/edit/<?php echo $hospital['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteModal" 
                                                    data-id="<?php echo $hospital['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($hospital['name']); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0"><?php echo __('export_options'); ?></h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <form action="<?php echo getBaseUrl(); ?>/hospitals/export" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <div class="mb-3">
                        <label for="export_type" class="form-label"><?php echo __('export_format'); ?></label>
                        <select class="form-select" id="export_type" name="export_type">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-download me-2"></i><?php echo __('export'); ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('confirm_delete_hospital'); ?> <span id="hospitalName"></span>?</p>
                <p class="text-danger"><?php echo __('delete_hospital_warning'); ?></p>
            </div>
            <div class="modal-footer">
                <form action="<?php echo getBaseUrl(); ?>/hospitals/delete" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="id" id="deleteId">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $("#hospitals-table").DataTable({
            "language": {
                "url": "' . ($currentLanguage === "ar" ? "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json" : "//cdn.datatables.net/plug-ins/1.11.5/i18n/en-GB.json") . '"
            },
            "order": [[0, "asc"]]
        });
        
        // Set up delete modal
        $("#deleteModal").on("show.bs.modal", function(event) {
            var button = $(event.relatedTarget);
            var id = button.data("id");
            var name = button.data("name");
            
            $("#deleteId").val(id);
            $("#hospitalName").text(name);
        });
    });
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>

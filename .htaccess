RewriteEngine On

# Handle front controller pattern
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]

# Set default character set
AddDefaultCharset UTF-8

# Enable PHP error logging
php_flag log_errors on
php_value error_log logs/error.log

# Disable directory listing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Protect config files
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect sensitive directories
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Set security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' https://cdn.jsdelivr.net https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self'"
</IfModule>

# Enable GZIP compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    # Session settings
    php_value session.cookie_httponly 1
    php_value session.use_only_cookies 1
    php_value session.cookie_secure 0
    
    # Error reporting (disable in production)
    php_value display_errors 1
    php_value display_startup_errors 1
    
    # Maximum upload file size
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # Maximum execution time
    php_value max_execution_time 30
    
    # Maximum input time
    php_value max_input_time 60
    
    # Memory limit
    php_value memory_limit 128M
</IfModule>

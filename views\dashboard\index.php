<?php
/**
 * Dashboard View
 * 
 * This file displays the dashboard.
 */

// Set page title
$pageTitle = __('dashboard');

// Start output buffering
ob_start();
?>

<div class="row mb-4">
    <div class="col-md-3 mb-4">
        <div class="card stats-card primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo $stats['devices']; ?></div>
                        <div class="stats-text"><?php echo __('devices'); ?></div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo $stats['devices_operational']; ?></div>
                        <div class="stats-text"><?php echo __('operational'); ?></div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo $stats['devices_maintenance']; ?></div>
                        <div class="stats-text"><?php echo __('under_maintenance'); ?></div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card danger h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?php echo $stats['devices_out_of_order']; ?></div>
                        <div class="stats-text"><?php echo __('out_of_order'); ?></div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('recent_tickets'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-sm btn-outline-primary"><?php echo __('view_all'); ?></a>
            </div>
            <div class="card-body">
                <?php if (empty($recentTickets)): ?>
                    <p class="text-muted"><?php echo __('no_tickets'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('title'); ?></th>
                                    <th><?php echo __('device'); ?></th>
                                    <th><?php echo __('priority'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentTickets as $ticket): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($ticket['title']); ?></td>
                                        <td><?php echo htmlspecialchars($ticket['device_name']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $ticket['priority'] === 'low' ? 'success' : 
                                                    ($ticket['priority'] === 'medium' ? 'warning' : 
                                                        ($ticket['priority'] === 'high' ? 'danger' : 'danger')); 
                                            ?>">
                                                <?php echo __($ticket['priority']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $ticket['status'] === 'open' ? 'primary' : 
                                                    ($ticket['status'] === 'in_progress' ? 'warning' : 
                                                        ($ticket['status'] === 'resolved' ? 'success' : 'secondary')); 
                                            ?>">
                                                <?php echo __($ticket['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('upcoming_maintenance'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-sm btn-outline-primary"><?php echo __('view_all'); ?></a>
            </div>
            <div class="card-body">
                <?php if (empty($upcomingMaintenance)): ?>
                    <p class="text-muted"><?php echo __('no_upcoming_maintenance'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('title'); ?></th>
                                    <th><?php echo __('device'); ?></th>
                                    <th><?php echo __('scheduled_date'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcomingMaintenance as $schedule): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($schedule['title']); ?></td>
                                        <td><?php echo htmlspecialchars($schedule['device_name']); ?></td>
                                        <td><?php echo formatDate($schedule['scheduled_date']); ?></td>
                                        <td>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (hasPermission('manage_maintenance')): ?>
                                                <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('overdue_maintenance'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-sm btn-outline-primary"><?php echo __('view_all'); ?></a>
            </div>
            <div class="card-body">
                <?php if (empty($overdueMaintenance)): ?>
                    <p class="text-muted"><?php echo __('no_overdue_maintenance'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('title'); ?></th>
                                    <th><?php echo __('device'); ?></th>
                                    <th><?php echo __('scheduled_date'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($overdueMaintenance as $schedule): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($schedule['title']); ?></td>
                                        <td><?php echo htmlspecialchars($schedule['device_name']); ?></td>
                                        <td class="text-danger"><?php echo formatDate($schedule['scheduled_date']); ?></td>
                                        <td>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (hasPermission('manage_maintenance')): ?>
                                                <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('ticket_status'); ?></h5>
                <a href="<?php echo getBaseUrl(); ?>/reports/ticket_resolution" class="btn btn-sm btn-outline-primary"><?php echo __('view_report'); ?></a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('open'); ?></div>
                                    <div><strong><?php echo $stats['tickets_open']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('in_progress'); ?></div>
                                    <div><strong><?php echo $stats['tickets_in_progress']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('resolved'); ?></div>
                                    <div><strong><?php echo $stats['tickets_resolved']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-secondary text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('closed'); ?></div>
                                    <div><strong><?php echo $stats['tickets_closed']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="<?php echo getBaseUrl(); ?>/tickets/create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        <?php echo __('create_ticket'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>

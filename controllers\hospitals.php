<?php
/**
 * Hospitals Controller
 * 
 * This file handles hospital-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$hospitalId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Check if the user has permission to view hospitals
        if (!hasPermission('view_hospitals') && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get all hospitals
        if ($userHospitalId && !hasPermission('manage_hospitals')) {
            // Non-admin users with hospital assignment can only see their hospital
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            // Admin can see all hospitals
            $hospitals = $hospitalModel->getAll();
        }
        
        // Include the hospitals index view
        include 'views/hospitals/index.php';
        break;
        
    case 'create':
        // Check if the user has permission to manage hospitals
        requirePermission('manage_hospitals');
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $hospitalData = [
                'name' => $_POST['name'] ?? '',
                'address' => $_POST['address'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'email' => $_POST['email'] ?? ''
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($hospitalData['name'])) {
                $errors['name'] = __('required_field');
            }
            
            if (empty($hospitalData['address'])) {
                $errors['address'] = __('required_field');
            }
            
            if (!empty($hospitalData['email']) && !filter_var($hospitalData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }
            
            // If no errors, create the hospital
            if (empty($errors)) {
                $hospitalId = $hospitalModel->create($hospitalData);
                
                if ($hospitalId) {
                    // Log the action
                    logAction('create_hospital', 'Created hospital: ' . $hospitalData['name']);
                    
                    // Set flash message
                    setFlashMessage('success', __('created_successfully', [__('hospital')]));
                    
                    // Redirect to hospitals index
                    redirect('hospitals');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }
        } else {
            // Initialize empty hospital data
            $hospitalData = [
                'name' => '',
                'address' => '',
                'phone' => '',
                'email' => ''
            ];
            
            $errors = [];
        }
        
        // Include the hospitals create view
        include 'views/hospitals/create.php';
        break;
        
    case 'edit':
        // Check if the user has permission to manage hospitals
        requirePermission('manage_hospitals');
        
        // Get the hospital
        $hospital = $hospitalModel->getById($hospitalId);
        
        // Check if the hospital exists
        if (!$hospital) {
            setFlashMessage('error', __('not_found'));
            redirect('hospitals');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $hospitalData = [
                'name' => $_POST['name'] ?? $hospital['name'],
                'address' => $_POST['address'] ?? $hospital['address'],
                'phone' => $_POST['phone'] ?? $hospital['phone'],
                'email' => $_POST['email'] ?? $hospital['email']
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($hospitalData['name'])) {
                $errors['name'] = __('required_field');
            }
            
            if (empty($hospitalData['address'])) {
                $errors['address'] = __('required_field');
            }
            
            if (!empty($hospitalData['email']) && !filter_var($hospitalData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }
            
            // If no errors, update the hospital
            if (empty($errors)) {
                $result = $hospitalModel->update($hospitalId, $hospitalData);
                
                if ($result) {
                    // Log the action
                    logAction('update_hospital', 'Updated hospital: ' . $hospitalData['name']);
                    
                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('hospital')]));
                    
                    // Redirect to hospitals index
                    redirect('hospitals');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }
        } else {
            $hospitalData = $hospital;
            $errors = [];
        }
        
        // Include the hospitals edit view
        include 'views/hospitals/edit.php';
        break;
        
    case 'delete':
        // Check if the user has permission to manage hospitals
        requirePermission('manage_hospitals');
        
        // Get the hospital
        $hospital = $hospitalModel->getById($hospitalId);
        
        // Check if the hospital exists
        if (!$hospital) {
            setFlashMessage('error', __('not_found'));
            redirect('hospitals');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $hospitalModel->delete($hospitalId);
            
            if ($result) {
                // Log the action
                logAction('delete_hospital', 'Deleted hospital: ' . $hospital['name']);
                
                // Set flash message
                setFlashMessage('success', __('deleted_successfully', [__('hospital')]));
            } else {
                setFlashMessage('error', __('delete_failed'));
            }
            
            // Redirect to hospitals index
            redirect('hospitals');
        }
        
        // Include the hospitals delete view
        include 'views/hospitals/delete.php';
        break;
        
    case 'view':
        // Check if the user has permission to view hospitals
        if (!hasPermission('view_hospitals') && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get the hospital
        $hospital = $hospitalModel->getById($hospitalId);
        
        // Check if the hospital exists
        if (!$hospital) {
            setFlashMessage('error', __('not_found'));
            redirect('hospitals');
        }
        
        // Check if the user has permission to view this hospital
        if ($userHospitalId && $hospital['id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('hospitals');
        }
        
        // Get hospital statistics
        $stats = $hospitalModel->getStatistics($hospitalId);
        
        // Get departments in this hospital
        $departments = $departmentModel->getByHospital($hospitalId);
        
        // Get devices in this hospital
        $devices = $deviceModel->getAll($hospitalId);
        
        // Include the hospitals view
        include 'views/hospitals/view.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}

<?php
/**
 * Main Layout
 *
 * This file contains the main layout for the application.
 */

// Get the current user
$currentUser = getCurrentUser();

// Get the current language
$currentLanguage = getCurrentLanguage();

// Get unread notifications count
$unreadNotificationsCount = countUnreadNotifications($currentUser['id']);
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLanguage; ?>" dir="<?php echo $currentLanguage === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo __('app_name'); ?></title>

    <!-- Bootstrap CSS -->
    <?php if ($currentLanguage === 'ar'): ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <?php else: ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <?php endif; ?>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getBaseUrl(); ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?php echo getBaseUrl(); ?>/assets/css/dark-mode.css">
</head>
<body class="<?php echo $currentLanguage === 'ar' ? 'rtl' : 'ltr'; ?><?php echo isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true' ? ' dark-mode' : ''; ?>">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <div class="text-center mb-4">
                        <h5 class="text-white"><?php echo __('app_name'); ?></h5>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('dashboard.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                <?php echo __('dashboard'); ?>
                            </a>
                        </li>

                        <?php if (hasPermission('view_hospitals') || hasPermission('manage_hospitals')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('hospitals.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/hospitals">
                                <i class="fas fa-hospital me-2"></i>
                                <?php echo __('hospitals'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_departments') || hasPermission('manage_departments')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('departments.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/departments">
                                <i class="fas fa-building me-2"></i>
                                <?php echo __('departments'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_devices') || hasPermission('manage_devices')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('devices.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/devices">
                                <i class="fas fa-microscope me-2"></i>
                                <?php echo __('devices'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_maintenance') || hasPermission('manage_maintenance')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('maintenance.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/maintenance">
                                <i class="fas fa-tools me-2"></i>
                                <?php echo __('maintenance'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_tickets') || hasPermission('manage_tickets') || hasPermission('create_tickets')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('tickets.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/tickets">
                                <i class="fas fa-ticket-alt me-2"></i>
                                <?php echo __('tickets'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_reports')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('reports.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                <?php echo __('reports'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('manage_users')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('users.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/users">
                                <i class="fas fa-users me-2"></i>
                                <?php echo __('users'); ?>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>

                    <hr class="text-white-50">

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('profile.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/profile">
                                <i class="fas fa-user me-2"></i>
                                <?php echo __('profile'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo getBaseUrl(); ?>/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                <?php echo __('logout'); ?>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo isset($pageTitle) ? $pageTitle : __('dashboard'); ?></h1>

                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo getBaseUrl(); ?>/notifications" class="btn btn-sm btn-outline-secondary position-relative">
                                <i class="fas fa-bell"></i>
                                <?php if ($unreadNotificationsCount > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?php echo $unreadNotificationsCount; ?>
                                </span>
                                <?php endif; ?>
                            </a>

                            <div class="dropdown me-2">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-globe"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                                    <li><a class="dropdown-item <?php echo $currentLanguage === 'en' ? 'active' : ''; ?>" href="?lang=en">English</a></li>
                                    <li><a class="dropdown-item <?php echo $currentLanguage === 'ar' ? 'active' : ''; ?>" href="?lang=ar">العربية</a></li>
                                </ul>
                            </div>

                            <button class="btn btn-sm btn-outline-secondary dark-mode-toggle" id="darkModeToggle" title="<?php echo __('toggle_dark_mode'); ?>">
                                <i class="fas <?php echo isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true' ? 'fa-sun' : 'fa-moon'; ?>"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['flash_message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['flash_message']['type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['flash_message']['message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['flash_message']); ?>
                <?php endif; ?>

                <?php
                // Include the content
                if (isset($content)) {
                    echo $content;
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Function to get departments by hospital ID
        function getDepartmentsByHospital(hospitalId, departmentSelectId) {
            if (!hospitalId) {
                return;
            }

            $.ajax({
                url: '<?php echo getBaseUrl(); ?>/api/get_departments',
                type: 'GET',
                data: { hospital_id: hospitalId },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var departments = response.data;
                        var departmentSelect = $('#' + departmentSelectId);

                        departmentSelect.empty();
                        departmentSelect.append('<option value=""><?php echo __('select_department'); ?></option>');

                        $.each(departments, function(index, department) {
                            departmentSelect.append('<option value="' + department.id + '">' + department.name + '</option>');
                        });
                    }
                }
            });
        }

        // Function to get devices by hospital ID and/or department ID
        function getDevicesByHospitalAndDepartment(hospitalId, departmentId, deviceSelectId) {
            $.ajax({
                url: '<?php echo getBaseUrl(); ?>/api/get_devices',
                type: 'GET',
                data: {
                    hospital_id: hospitalId,
                    department_id: departmentId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var devices = response.data;
                        var deviceSelect = $('#' + deviceSelectId);

                        deviceSelect.empty();
                        deviceSelect.append('<option value=""><?php echo __('select_device'); ?></option>');

                        $.each(devices, function(index, device) {
                            deviceSelect.append('<option value="' + device.id + '">' + device.name + ' (' + device.serial_number + ')</option>');
                        });
                    }
                }
            });
        }

        // Function to get notifications
        function getNotifications() {
            $.ajax({
                url: '<?php echo getBaseUrl(); ?>/api/get_notifications',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var count = response.data.count;
                        var notificationBadge = $('.notification-badge');

                        if (count > 0) {
                            notificationBadge.text(count).show();
                        } else {
                            notificationBadge.hide();
                        }
                    }
                }
            });
        }

        // Dark mode toggle
        $(document).ready(function() {
            // Toggle dark mode
            $('#darkModeToggle').click(function() {
                // Toggle body class
                $('body').toggleClass('dark-mode');

                // Toggle icon
                $(this).find('i').toggleClass('fa-moon fa-sun');

                // Set cookie
                var isDarkMode = $('body').hasClass('dark-mode');
                document.cookie = 'dark_mode=' + isDarkMode + '; path=/; max-age=31536000'; // 1 year
            });
        });

        // Check for new notifications every 60 seconds
        setInterval(getNotifications, 60000);
    </script>

    <?php if (isset($scripts)): ?>
        <?php echo $scripts; ?>
    <?php endif; ?>
</body>
</html>

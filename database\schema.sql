-- Medical Device Management System Database Schema

-- Drop database if it exists
DROP DATABASE IF EXISTS medical_device_management;

-- <PERSON>reate database
CREATE DATABASE medical_device_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use database
USE medical_device_management;

-- Create users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'engineer', 'technician', 'staff') NOT NULL DEFAULT 'staff',
    hospital_id INT NULL,
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    last_login DATETIME NULL,
    status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (hospital_id),
    INDEX (role)
);

-- Create hospitals table
CREATE TABLE hospitals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    website VARCHAR(255) NULL,
    notes TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (name)
);

-- Create departments table
CREATE TABLE departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hospital_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NULL,
    email VARCHAR(100) NULL,
    notes TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE,
    INDEX (hospital_id),
    INDEX (name)
);

-- Create devices table
CREATE TABLE devices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hospital_id INT NOT NULL,
    department_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    serial_number VARCHAR(100) NOT NULL UNIQUE,
    manufacturer VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    purchase_date DATE NOT NULL,
    warranty_expiry DATE NOT NULL,
    status ENUM('operational', 'under_maintenance', 'out_of_order', 'retired') NOT NULL DEFAULT 'operational',
    maintenance_interval INT NULL COMMENT 'Maintenance interval in days',
    last_maintenance_date DATE NULL,
    next_maintenance_date DATE NULL,
    qr_code VARCHAR(255) NULL,
    location VARCHAR(100) NULL,
    notes TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    INDEX (hospital_id),
    INDEX (department_id),
    INDEX (status),
    INDEX (serial_number)
);

-- Create maintenance_schedules table
CREATE TABLE maintenance_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT NULL,
    scheduled_date DATE NOT NULL,
    frequency ENUM('once', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly') NOT NULL DEFAULT 'once',
    status ENUM('scheduled', 'completed', 'overdue', 'cancelled') NOT NULL DEFAULT 'scheduled',
    priority ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium',
    created_by INT NOT NULL,
    notes TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX (device_id),
    INDEX (scheduled_date),
    INDEX (status)
);

-- Create maintenance_logs table
CREATE TABLE maintenance_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    maintenance_schedule_id INT NULL,
    device_id INT NOT NULL,
    performed_by INT NOT NULL,
    performed_date DATE NOT NULL,
    maintenance_date DATE NOT NULL,
    actions_taken TEXT NULL,
    parts_replaced TEXT NULL,
    results TEXT NULL,
    recommendations TEXT NULL,
    status ENUM('completed', 'incomplete') NOT NULL DEFAULT 'completed',
    notes TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maintenance_schedule_id) REFERENCES maintenance_schedules(id) ON DELETE SET NULL,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (performed_by) REFERENCES users(id),
    INDEX (device_id),
    INDEX (performed_date)
);

-- Create tickets table
CREATE TABLE tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_id INT NOT NULL,
    reported_by INT NOT NULL,
    assigned_to INT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    status ENUM('open', 'in_progress', 'resolved', 'closed') NOT NULL DEFAULT 'open',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_by) REFERENCES users(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX (device_id),
    INDEX (status),
    INDEX (priority)
);

-- Create ticket_updates table
CREATE TABLE ticket_updates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    user_id INT NOT NULL,
    comment TEXT NOT NULL,
    status_change VARCHAR(20) NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX (ticket_id)
);

-- Create notifications table
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    reference_id INT NULL,
    reference_type VARCHAR(50) NULL,
    is_read BOOLEAN NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX (user_id),
    INDEX (is_read)
);

-- Create settings table
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create security_logs table
CREATE TABLE security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event VARCHAR(50) NOT NULL,
    username VARCHAR(50) NULL,
    ip_address VARCHAR(45) NOT NULL,
    details TEXT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX (event),
    INDEX (username),
    INDEX (created_at)
);

-- Create user_permissions table
CREATE TABLE user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission VARCHAR(50) NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, permission),
    INDEX (user_id)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, email, full_name, role, language)
VALUES ('admin', '$2y$10$8zf0bvFUxHC8Wl7LQ1M3leZRqQBiQQx5uNDpP/NLqD0Pu5GXEfEju', '<EMAIL>', 'System Administrator', 'admin', 'en');

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, description)
VALUES
('site_name', 'Medical Device Management System', 'The name of the site'),
('site_description', 'A system for managing medical devices in hospitals', 'The description of the site'),
('email_from', '<EMAIL>', 'The email address to send emails from'),
('email_from_name', 'Medical Device Management System', 'The name to send emails from'),
('smtp_host', '', 'SMTP host for sending emails'),
('smtp_port', '587', 'SMTP port for sending emails'),
('smtp_username', '', 'SMTP username for sending emails'),
('smtp_password', '', 'SMTP password for sending emails'),
('smtp_encryption', 'tls', 'SMTP encryption for sending emails'),
('maintenance_notification_days', '7', 'Number of days before maintenance to send notification'),
('warranty_expiry_notification_days', '30', 'Number of days before warranty expiry to send notification'),
('default_language', 'en', 'Default language for the system'),
('enable_qr_codes', '1', 'Enable QR codes for devices'),
('enable_email_notifications', '0', 'Enable email notifications');

-- Add foreign key constraint to users table
ALTER TABLE users
ADD CONSTRAINT fk_users_hospital
FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE SET NULL;

-- Create remember_tokens table
CREATE TABLE remember_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create password_resets table
CREATE TABLE password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_reset (user_id)
);

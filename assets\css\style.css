/**
 * Medical Device Management System
 * Custom CSS Styles
 */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #343a40;
}

.rtl .sidebar {
    right: 0;
    left: auto;
    box-shadow: inset 1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, .75);
    padding: .75rem 1rem;
}

.sidebar .nav-link:hover {
    color: #fff;
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, .1);
}

.sidebar .nav-link .feather {
    margin-right: 4px;
    color: rgba(255, 255, 255, .5);
}

.rtl .sidebar .nav-link .feather {
    margin-right: 0;
    margin-left: 4px;
}

.sidebar .nav-link:hover .feather,
.sidebar .nav-link.active .feather {
    color: inherit;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
    color: rgba(255, 255, 255, .5);
    padding: 1rem;
}

/* Main Content */
.main-content {
    margin-left: 240px;
    padding: 2rem;
}

.rtl .main-content {
    margin-left: 0;
    margin-right: 240px;
}

@media (max-width: 767.98px) {
    .main-content {
        margin-left: 0;
    }
    
    .rtl .main-content {
        margin-right: 0;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .sidebar-sticky {
        height: auto;
    }
}

/* Navbar */
.navbar-brand {
    padding-top: .75rem;
    padding-bottom: .75rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, .25);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

.rtl .navbar-brand {
    box-shadow: inset 1px 0 0 rgba(0, 0, 0, .25);
}

.navbar .navbar-toggler {
    top: .25rem;
    right: 1rem;
}

.rtl .navbar .navbar-toggler {
    right: auto;
    left: 1rem;
}

.navbar .form-control {
    padding: .75rem 1rem;
    border-width: 0;
    border-radius: 0;
}

/* Cards */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem;
}

.card-body {
    padding: 1.25rem;
}

/* Dashboard Stats */
.stats-card {
    border-left: 4px solid #007bff;
    transition: transform 0.3s;
}

.rtl .stats-card {
    border-left: none;
    border-right: 4px solid #007bff;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card.primary {
    border-color: #007bff;
}

.stats-card.success {
    border-color: #28a745;
}

.stats-card.warning {
    border-color: #ffc107;
}

.stats-card.danger {
    border-color: #dc3545;
}

.stats-card .card-body {
    padding: 1rem;
}

.stats-card .stats-icon {
    font-size: 2rem;
    opacity: 0.3;
}

.stats-card .stats-number {
    font-size: 1.5rem;
    font-weight: bold;
}

.stats-card .stats-text {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Tables */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Status Badges */
.badge-operational {
    background-color: #28a745;
    color: #fff;
}

.badge-under-maintenance {
    background-color: #ffc107;
    color: #212529;
}

.badge-out-of-order {
    background-color: #dc3545;
    color: #fff;
}

.badge-retired {
    background-color: #6c757d;
    color: #fff;
}

.badge-scheduled {
    background-color: #17a2b8;
    color: #fff;
}

.badge-completed {
    background-color: #28a745;
    color: #fff;
}

.badge-overdue {
    background-color: #dc3545;
    color: #fff;
}

.badge-cancelled {
    background-color: #6c757d;
    color: #fff;
}

.badge-open {
    background-color: #17a2b8;
    color: #fff;
}

.badge-in-progress {
    background-color: #ffc107;
    color: #212529;
}

.badge-resolved {
    background-color: #28a745;
    color: #fff;
}

.badge-closed {
    background-color: #6c757d;
    color: #fff;
}

.badge-low {
    background-color: #28a745;
    color: #fff;
}

.badge-medium {
    background-color: #ffc107;
    color: #212529;
}

.badge-high {
    background-color: #fd7e14;
    color: #fff;
}

.badge-critical {
    background-color: #dc3545;
    color: #fff;
}

/* Forms */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 500;
}

.form-text {
    color: #6c757d;
}

/* Buttons */
.btn {
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
}

.btn-icon {
    padding: 0.25rem 0.5rem;
}

.btn-icon i {
    font-size: 0.875rem;
}

/* QR Code */
.qr-code-container {
    text-align: center;
    margin: 1rem 0;
}

.qr-code-image {
    max-width: 200px;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    background-color: #fff;
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1;
    border-radius: 10rem;
}

.rtl .notification-badge {
    right: auto;
    left: 5px;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: rgba(0, 123, 255, 0.05);
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Login Page */
.login-container {
    max-width: 400px;
    margin: 5rem auto;
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.login-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Footer */
.footer {
    padding: 1.5rem 0;
    color: #6c757d;
    border-top: 1px solid #dee2e6;
    margin-top: 3rem;
}

/* Print Styles */
@media print {
    .sidebar, .navbar, .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    body {
        background-color: #fff !important;
    }
}
